package cmd

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/schollz/progressbar/v3"
	"github.com/spf13/cobra"
	"wappalyzer-cli/pkg/client"
	"wappalyzer-cli/pkg/detector"
	"wappalyzer-cli/pkg/output"
	"wappalyzer-cli/pkg/worker"
)

var (
	// 全局配置变量
	targetURL    string
	inputFile    string
	outputFormat string
	outputFile   string
	concurrency  int
	timeout      int
	retries      int
	userAgent    string
	proxy        string
	silent       bool
	verbose      bool
	showProgress bool
	configFile   string
)

// rootCmd 代表基础命令
var rootCmd = &cobra.Command{
	Use:   "wappalyzer-cli",
	Short: "Web服务指纹识别CLI工具",
	Long: `一个高性能的Web服务指纹识别CLI工具，基于wappalyzergo库开发。

支持功能：
- 单个URL或批量URL检测
- 多种输出格式：JSON、表格、简洁模式
- 并发处理提升性能
- 完善的错误处理和重试机制

示例用法：
  wappalyzer-cli -u https://example.com
  wappalyzer-cli -i urls.txt -o json
  echo "https://example.com" | wappalyzer-cli -f table`,
	Run: runDetection,
}

// Execute 添加所有子命令到根命令并设置标志
func Execute() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func init() {
	// 输入选项
	rootCmd.Flags().StringVarP(&targetURL, "url", "u", "", "要检测的目标URL")
	rootCmd.Flags().StringVarP(&inputFile, "input", "i", "", "包含URL列表的输入文件")
	
	// 输出选项
	rootCmd.Flags().StringVarP(&outputFormat, "format", "f", "text", "输出格式 (text|json|table|simple)")
	rootCmd.Flags().StringVarP(&outputFile, "output", "o", "", "输出文件路径")
	
	// 性能选项
	rootCmd.Flags().IntVarP(&concurrency, "concurrency", "c", 10, "并发数量")
	rootCmd.Flags().IntVarP(&timeout, "timeout", "t", 10, "HTTP超时时间(秒)")
	rootCmd.Flags().IntVarP(&retries, "retries", "r", 3, "重试次数")
	
	// HTTP选项
	rootCmd.Flags().StringVar(&userAgent, "user-agent", "wappalyzer-cli/1.0", "自定义User-Agent")
	rootCmd.Flags().StringVar(&proxy, "proxy", "", "代理服务器 (http://proxy:port)")
	
	// 其他选项
	rootCmd.Flags().BoolVarP(&silent, "silent", "s", false, "静默模式，只输出结果")
	rootCmd.Flags().BoolVarP(&verbose, "verbose", "v", false, "详细输出模式")
	rootCmd.Flags().BoolVarP(&showProgress, "progress", "p", true, "显示进度条")
	rootCmd.Flags().StringVar(&configFile, "config", "", "配置文件路径")
}

// runDetection 主要的检测逻辑
func runDetection(cmd *cobra.Command, args []string) {
	// 验证输入参数
	if targetURL == "" && inputFile == "" && len(args) == 0 {
		// 检查是否有stdin输入
		stat, _ := os.Stdin.Stat()
		if (stat.Mode() & os.ModeCharDevice) != 0 {
			fmt.Println("错误: 必须提供URL (-u), 输入文件 (-i), 或通过stdin输入")
			cmd.Help()
			os.Exit(1)
		}
	}
	
	// 验证输出格式
	if outputFormat != "text" && outputFormat != "json" && outputFormat != "table" && outputFormat != "simple" {
		fmt.Println("错误: 输出格式必须是 text, json, table 或 simple")
		os.Exit(1)
	}
	
	// 验证并发数
	if concurrency < 1 || concurrency > 100 {
		fmt.Println("错误: 并发数必须在1-100之间")
		os.Exit(1)
	}
	
	if !silent {
		fmt.Printf("🔍 Web服务指纹识别工具启动\n")
		fmt.Printf("📊 配置: 并发=%d, 超时=%ds, 重试=%d次\n", concurrency, timeout, retries)
		fmt.Printf("📝 输出格式: %s\n", outputFormat)
		if outputFile != "" {
			fmt.Printf("📁 输出文件: %s\n", outputFile)
		}
		fmt.Println()
	}
	
	// 创建HTTP客户端
	httpClient, err := client.NewHTTPClient(client.Config{
		UserAgent: userAgent,
		Timeout:   timeout,
		Retries:   retries,
		Proxy:     proxy,
	})
	if err != nil {
		fmt.Printf("❌ 创建HTTP客户端失败: %v\n", err)
		os.Exit(1)
	}
	defer httpClient.Close()

	// 创建检测器
	det, err := detector.NewDetector(httpClient)
	if err != nil {
		fmt.Printf("❌ 创建检测器失败: %v\n", err)
		os.Exit(1)
	}

	// 创建输出格式化器
	formatter, err := output.NewFormatter(outputFormat, outputFile, silent)
	if err != nil {
		fmt.Printf("❌ 创建输出格式化器失败: %v\n", err)
		os.Exit(1)
	}
	defer formatter.Close()

	// 收集要检测的URL
	urls, err := collectURLs()
	if err != nil {
		formatter.PrintError(fmt.Sprintf("收集URL失败: %v", err))
		os.Exit(1)
	}

	if len(urls) == 0 {
		formatter.PrintError("没有找到要检测的URL")
		os.Exit(1)
	}

	// 开始检测
	startTime := time.Now()

	if len(urls) == 1 {
		// 单个URL检测
		result := det.DetectURL(urls[0])
		if err := formatter.PrintResult(result); err != nil {
			formatter.PrintError(fmt.Sprintf("输出结果失败: %v", err))
			os.Exit(1)
		}
	} else {
		// 批量URL检测 - 使用worker pool并发处理
		pool := worker.NewPool(det, concurrency)
		pool.Start()
		defer pool.Stop()

		// 创建进度条
		var bar *progressbar.ProgressBar
		if showProgress && !silent {
			bar = progressbar.NewOptions(len(urls),
				progressbar.OptionSetDescription("🔍 检测中..."),
				progressbar.OptionSetWriter(os.Stderr),
				progressbar.OptionShowCount(),
				progressbar.OptionShowIts(),
				progressbar.OptionSetItsString("URLs"),
				progressbar.OptionSetTheme(progressbar.Theme{
					Saucer:        "█",
					SaucerHead:    "█",
					SaucerPadding: "░",
					BarStart:      "[",
					BarEnd:        "]",
				}),
			)
		}

		// 进度回调函数
		progressCallback := func(completed, total int, currentURL string) {
			if bar != nil {
				bar.Add(1)
			} else if !silent && verbose {
				fmt.Fprintf(os.Stderr, "🔍 检测进度: %d/%d - %s\n", completed, total, currentURL)
			}
		}

		// 并发处理所有URL
		results := pool.ProcessURLs(urls, progressCallback)

		// 完成进度条
		if bar != nil {
			bar.Finish()
			fmt.Fprintf(os.Stderr, "\n")
		}

		if err := formatter.PrintResults(results); err != nil {
			formatter.PrintError(fmt.Sprintf("输出结果失败: %v", err))
			os.Exit(1)
		}
	}

	// 输出完成信息
	duration := time.Since(startTime)
	if !silent {
		formatter.PrintSuccess(fmt.Sprintf("检测完成，耗时: %v", duration))
	}
}

// collectURLs 收集要检测的URL
func collectURLs() ([]string, error) {
	var urls []string

	// 从命令行参数获取URL
	if targetURL != "" {
		urls = append(urls, targetURL)
	}

	// 从输入文件读取URL
	if inputFile != "" {
		fileURLs, err := readURLsFromFile(inputFile)
		if err != nil {
			return nil, fmt.Errorf("读取文件失败: %v", err)
		}
		urls = append(urls, fileURLs...)
	}

	// 从stdin读取URL
	if targetURL == "" && inputFile == "" {
		stat, _ := os.Stdin.Stat()
		if (stat.Mode() & os.ModeCharDevice) == 0 {
			stdinURLs, err := readURLsFromStdin()
			if err != nil {
				return nil, fmt.Errorf("读取stdin失败: %v", err)
			}
			urls = append(urls, stdinURLs...)
		}
	}

	// 去重和清理URL
	return cleanAndDeduplicateURLs(urls), nil
}

// readURLsFromFile 从文件读取URL
func readURLsFromFile(filename string) ([]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var urls []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" && !strings.HasPrefix(line, "#") {
			urls = append(urls, line)
		}
	}

	return urls, scanner.Err()
}

// readURLsFromStdin 从stdin读取URL
func readURLsFromStdin() ([]string, error) {
	var urls []string
	scanner := bufio.NewScanner(os.Stdin)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" && !strings.HasPrefix(line, "#") {
			urls = append(urls, line)
		}
	}

	return urls, scanner.Err()
}

// cleanAndDeduplicateURLs 清理和去重URL
func cleanAndDeduplicateURLs(urls []string) []string {
	seen := make(map[string]bool)
	var result []string

	for _, url := range urls {
		url = strings.TrimSpace(url)
		if url == "" {
			continue
		}

		// 简单的URL格式验证
		if !strings.Contains(url, ".") {
			continue
		}

		if !seen[url] {
			seen[url] = true
			result = append(result, url)
		}
	}

	return result
}
