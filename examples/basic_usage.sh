#!/bin/bash

# Wappalyzer CLI 基本使用示例

echo "=== Wappalyzer CLI 使用示例 ==="
echo

# 1. 单个URL检测
echo "1. 检测单个URL:"
go run ../main.go -u https://httpbin.org/get
echo

# 2. 批量URL检测
echo "2. 批量URL检测:"
cat > urls.txt << EOF
https://httpbin.org/get
https://httpbin.org/json
https://httpbin.org/html
EOF

go run ../main.go -i urls.txt
echo

# 3. 不同输出格式
echo "3. JSON格式输出:"
go run ../main.go -u https://httpbin.org/get -f json
echo

echo "4. 简洁格式输出:"
go run ../main.go -i urls.txt -f simple
echo

echo "5. 表格格式输出:"
go run ../main.go -i urls.txt -f table
echo

# 6. 管道输入
echo "6. 管道输入:"
echo "https://httpbin.org/get" | go run ../main.go -f simple
echo

# 7. 性能调优
echo "7. 高并发检测:"
go run ../main.go -i urls.txt -c 5 -f simple
echo

# 8. 静默模式
echo "8. 静默模式:"
go run ../main.go -i urls.txt -s -f simple
echo

# 清理
rm -f urls.txt

echo "=== 示例完成 ==="
