package client

import (
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// HTTPClient 封装HTTP客户端配置
type HTTPClient struct {
	client    *http.Client
	userAgent string
	timeout   time.Duration
	retries   int
}

// Config HTTP客户端配置
type Config struct {
	UserAgent string
	Timeout   int
	Retries   int
	Proxy     string
}

// NewHTTPClient 创建新的HTTP客户端
func NewHTTPClient(config Config) (*HTTPClient, error) {
	// 创建传输配置
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true, // 跳过SSL验证，用于测试
		},
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     30 * time.Second,
	}

	// 配置代理
	if config.Proxy != "" {
		proxyURL, err := url.Parse(config.Proxy)
		if err != nil {
			return nil, fmt.Errorf("无效的代理URL: %v", err)
		}
		transport.Proxy = http.ProxyURL(proxyURL)
	}

	// 创建HTTP客户端
	client := &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.Timeout) * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 限制重定向次数
			if len(via) >= 5 {
				return fmt.Errorf("重定向次数过多")
			}
			return nil
		},
	}

	return &HTTPClient{
		client:    client,
		userAgent: config.UserAgent,
		timeout:   time.Duration(config.Timeout) * time.Second,
		retries:   config.Retries,
	}, nil
}

// Response HTTP响应结构
type Response struct {
	URL        string
	StatusCode int
	Headers    http.Header
	Body       []byte
	Error      error
	ErrorType  string // 错误类型：network, timeout, http, parse
}

// Get 发送GET请求
func (c *HTTPClient) Get(targetURL string) *Response {
	var lastErr error
	
	// 确保URL格式正确
	if !strings.HasPrefix(targetURL, "http://") && !strings.HasPrefix(targetURL, "https://") {
		targetURL = "https://" + targetURL
	}

	// 重试逻辑
	for attempt := 0; attempt <= c.retries; attempt++ {
		resp, err := c.doRequest(targetURL)
		if err == nil {
			return resp
		}
		lastErr = err
		
		// 如果不是最后一次尝试，等待一段时间再重试
		if attempt < c.retries {
			time.Sleep(time.Duration(attempt+1) * time.Second)
		}
	}

	// 分析错误类型
	errorType := "network"
	if lastErr != nil {
		errStr := lastErr.Error()
		if strings.Contains(errStr, "timeout") || strings.Contains(errStr, "deadline exceeded") {
			errorType = "timeout"
		} else if strings.Contains(errStr, "connection refused") || strings.Contains(errStr, "no such host") {
			errorType = "network"
		}
	}

	return &Response{
		URL:       targetURL,
		Error:     fmt.Errorf("请求失败，已重试%d次: %v", c.retries, lastErr),
		ErrorType: errorType,
	}
}

// doRequest 执行实际的HTTP请求
func (c *HTTPClient) doRequest(targetURL string) (*Response, error) {
	// 创建请求
	req, err := http.NewRequest("GET", targetURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", c.userAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	// 发送请求
	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求执行失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}

	return &Response{
		URL:        targetURL,
		StatusCode: resp.StatusCode,
		Headers:    resp.Header,
		Body:       body,
	}, nil
}

// Close 关闭HTTP客户端
func (c *HTTPClient) Close() {
	if transport, ok := c.client.Transport.(*http.Transport); ok {
		transport.CloseIdleConnections()
	}
}
