package output

import (
	"encoding/json"
	"fmt"
	"os"

	"wappalyzer-cli/pkg/detector"
)

// OutputFormat 输出格式类型
type OutputFormat string

const (
	FormatText   OutputFormat = "text"
	FormatJSON   OutputFormat = "json"
	FormatTable  OutputFormat = "table"
	FormatSimple OutputFormat = "simple"
)

// Formatter 输出格式化器
type Formatter struct {
	format OutputFormat
	file   *os.File
	silent bool
}

// NewFormatter 创建新的格式化器
func NewFormatter(format string, outputFile string, silent bool) (*Formatter, error) {
	var file *os.File = os.Stdout
	var err error

	// 如果指定了输出文件，创建文件
	if outputFile != "" {
		file, err = os.Create(outputFile)
		if err != nil {
			return nil, fmt.Errorf("创建输出文件失败: %v", err)
		}
	}

	return &Formatter{
		format: OutputFormat(format),
		file:   file,
		silent: silent,
	}, nil
}

// Close 关闭格式化器
func (f *Formatter) Close() error {
	if f.file != os.Stdout {
		return f.file.Close()
	}
	return nil
}

// PrintResult 输出单个检测结果
func (f *Formatter) PrintResult(result *detector.DetectionResult) error {
	switch f.format {
	case FormatJSON:
		return f.printJSON(result)
	case FormatTable:
		return f.printTable([]*detector.DetectionResult{result})
	case FormatSimple:
		return f.printSimple(result)
	default:
		return f.printText(result)
	}
}

// PrintResults 输出多个检测结果
func (f *Formatter) PrintResults(results []*detector.DetectionResult) error {
	switch f.format {
	case FormatJSON:
		return f.printJSONArray(results)
	case FormatTable:
		return f.printTable(results)
	case FormatSimple:
		return f.printSimpleArray(results)
	default:
		return f.printTextArray(results)
	}
}

// printJSON 输出JSON格式
func (f *Formatter) printJSON(result *detector.DetectionResult) error {
	data, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return fmt.Errorf("JSON序列化失败: %v", err)
	}
	
	_, err = f.file.Write(data)
	if err != nil {
		return fmt.Errorf("写入输出失败: %v", err)
	}
	
	f.file.WriteString("\n")
	return nil
}

// printJSONArray 输出JSON数组格式
func (f *Formatter) printJSONArray(results []*detector.DetectionResult) error {
	data, err := json.MarshalIndent(results, "", "  ")
	if err != nil {
		return fmt.Errorf("JSON序列化失败: %v", err)
	}
	
	_, err = f.file.Write(data)
	if err != nil {
		return fmt.Errorf("写入输出失败: %v", err)
	}
	
	f.file.WriteString("\n")
	return nil
}

// printText 输出文本格式
func (f *Formatter) printText(result *detector.DetectionResult) error {
	if result.HasError() {
		fmt.Fprintf(f.file, "❌ %s - 错误: %s\n", result.URL, result.Error)
		return nil
	}

	fmt.Fprintf(f.file, "🌐 %s [%d]\n", result.URL, result.StatusCode)
	
	if len(result.Technologies) == 0 {
		fmt.Fprintf(f.file, "   📋 未检测到技术栈\n")
	} else {
		fmt.Fprintf(f.file, "   📋 检测到 %d 个技术:\n", len(result.Technologies))
		for _, tech := range result.Technologies {
			fmt.Fprintf(f.file, "      • %s\n", tech.Name)
		}
	}
	
	if !f.silent {
		if server := result.Headers["Server"]; server != "" {
			fmt.Fprintf(f.file, "   🖥️  服务器: %s\n", server)
		}
		fmt.Fprintf(f.file, "   📊 响应大小: %d bytes\n", result.Meta["content_length"])
	}
	
	fmt.Fprintf(f.file, "\n")
	return nil
}

// printTextArray 输出文本数组格式
func (f *Formatter) printTextArray(results []*detector.DetectionResult) error {
	for _, result := range results {
		if err := f.printText(result); err != nil {
			return err
		}
	}
	
	// 输出统计信息
	if !f.silent && len(results) > 1 {
		successful := 0
		totalTechs := 0
		for _, result := range results {
			if result.IsSuccessful() {
				successful++
				totalTechs += len(result.Technologies)
			}
		}
		
		fmt.Fprintf(f.file, "📈 统计信息:\n")
		fmt.Fprintf(f.file, "   总URL数: %d\n", len(results))
		fmt.Fprintf(f.file, "   成功检测: %d\n", successful)
		fmt.Fprintf(f.file, "   失败数: %d\n", len(results)-successful)
		fmt.Fprintf(f.file, "   检测到技术总数: %d\n", totalTechs)
	}
	
	return nil
}

// printTable 输出表格格式
func (f *Formatter) printTable(results []*detector.DetectionResult) error {
	// 简单的表格格式输出
	fmt.Fprintf(f.file, "%-40s %-10s %-30s %-20s\n", "URL", "状态码", "技术栈", "服务器")
	fmt.Fprintf(f.file, "%s\n", "--------------------------------------------------------------------------------")

	for _, result := range results {
		status := fmt.Sprintf("%d", result.StatusCode)
		if result.HasError() {
			status = "错误"
		}

		technologies := result.GetTechnologyNamesString()
		if len(technologies) > 30 {
			technologies = technologies[:27] + "..."
		}

		server := result.Headers["Server"]
		if len(server) > 20 {
			server = server[:17] + "..."
		}

		url := result.URL
		if len(url) > 40 {
			url = url[:37] + "..."
		}

		fmt.Fprintf(f.file, "%-40s %-10s %-30s %-20s\n", url, status, technologies, server)
	}

	return nil
}

// printSimple 输出简洁格式
func (f *Formatter) printSimple(result *detector.DetectionResult) error {
	if result.HasError() {
		fmt.Fprintf(f.file, "%s ERROR: %s\n", result.URL, result.Error)
		return nil
	}

	technologies := result.GetTechnologyNamesString()
	if technologies == "无检测到技术栈" {
		technologies = "None"
	}

	fmt.Fprintf(f.file, "%s [%d] %s\n", result.URL, result.StatusCode, technologies)
	return nil
}

// printSimpleArray 输出简洁数组格式
func (f *Formatter) printSimpleArray(results []*detector.DetectionResult) error {
	for _, result := range results {
		if err := f.printSimple(result); err != nil {
			return err
		}
	}
	return nil
}

// PrintInfo 输出信息消息
func (f *Formatter) PrintInfo(message string) {
	if !f.silent {
		fmt.Fprintf(os.Stderr, "ℹ️  %s\n", message)
	}
}

// PrintError 输出错误消息
func (f *Formatter) PrintError(message string) {
	fmt.Fprintf(os.Stderr, "❌ %s\n", message)
}

// PrintSuccess 输出成功消息
func (f *Formatter) PrintSuccess(message string) {
	if !f.silent {
		fmt.Fprintf(os.Stderr, "✅ %s\n", message)
	}
}
