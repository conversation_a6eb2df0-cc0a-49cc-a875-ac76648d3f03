package detector

import (
	"testing"

	"wappalyzer-cli/pkg/client"
)

func TestNewDetector(t *testing.T) {
	// 创建HTTP客户端
	httpClient, err := client.NewHTTPClient(client.Config{
		UserAgent: "test-agent",
		Timeout:   10,
		Retries:   1,
	})
	if err != nil {
		t.Fatalf("创建HTTP客户端失败: %v", err)
	}

	// 创建检测器
	detector, err := NewDetector(httpClient)
	if err != nil {
		t.Fatalf("创建检测器失败: %v", err)
	}

	if detector == nil {
		t.Fatal("检测器不应该为nil")
	}

	if detector.wappalyzer == nil {
		t.Fatal("wappalyzer实例不应该为nil")
	}

	if detector.client == nil {
		t.Fatal("HTTP客户端不应该为nil")
	}
}

func TestDetectionResult_GetTechnologyNames(t *testing.T) {
	result := &DetectionResult{
		Technologies: []Technology{
			{Name: "Python"},
			{Name: "Django"},
			{Name: "PostgreSQL"},
		},
	}

	names := result.GetTechnologyNames()
	expected := []string{"Python", "Django", "PostgreSQL"}

	if len(names) != len(expected) {
		t.Fatalf("期望%d个技术名称，实际得到%d个", len(expected), len(names))
	}

	for i, name := range names {
		if name != expected[i] {
			t.Errorf("期望技术名称'%s'，实际得到'%s'", expected[i], name)
		}
	}
}

func TestDetectionResult_GetTechnologyNamesString(t *testing.T) {
	// 测试有技术的情况
	result := &DetectionResult{
		Technologies: []Technology{
			{Name: "Python"},
			{Name: "Django"},
		},
	}

	str := result.GetTechnologyNamesString()
	expected := "Python, Django"
	if str != expected {
		t.Errorf("期望'%s'，实际得到'%s'", expected, str)
	}

	// 测试无技术的情况
	emptyResult := &DetectionResult{
		Technologies: []Technology{},
	}

	emptyStr := emptyResult.GetTechnologyNamesString()
	expectedEmpty := "无检测到技术栈"
	if emptyStr != expectedEmpty {
		t.Errorf("期望'%s'，实际得到'%s'", expectedEmpty, emptyStr)
	}
}

func TestDetectionResult_HasError(t *testing.T) {
	// 测试有错误的情况
	resultWithError := &DetectionResult{
		Error: "网络错误",
	}

	if !resultWithError.HasError() {
		t.Error("应该检测到错误")
	}

	// 测试无错误的情况
	resultWithoutError := &DetectionResult{
		Error: "",
	}

	if resultWithoutError.HasError() {
		t.Error("不应该检测到错误")
	}
}

func TestDetectionResult_IsSuccessful(t *testing.T) {
	// 测试成功的情况
	successResult := &DetectionResult{
		StatusCode: 200,
		Error:      "",
	}

	if !successResult.IsSuccessful() {
		t.Error("应该是成功的结果")
	}

	// 测试有错误的情况
	errorResult := &DetectionResult{
		StatusCode: 200,
		Error:      "网络错误",
	}

	if errorResult.IsSuccessful() {
		t.Error("有错误的结果不应该是成功的")
	}

	// 测试HTTP错误状态码的情况
	httpErrorResult := &DetectionResult{
		StatusCode: 500,
		Error:      "",
	}

	if httpErrorResult.IsSuccessful() {
		t.Error("HTTP错误状态码不应该是成功的")
	}
}
