package detector

import (
	"fmt"
	"sort"
	"strings"

	wappalyzer "github.com/projectdiscovery/wappalyzergo"
	"wappalyzer-cli/pkg/client"
)

// Technology 检测到的技术信息
type Technology struct {
	Name        string            `json:"name"`
	Categories  []string          `json:"categories,omitempty"`
	Version     string            `json:"version,omitempty"`
	Confidence  int               `json:"confidence,omitempty"`
	Website     string            `json:"website,omitempty"`
	Description string            `json:"description,omitempty"`
	Icon        string            `json:"icon,omitempty"`
	CPE         string            `json:"cpe,omitempty"`
	Implies     []string          `json:"implies,omitempty"`
	Meta        map[string]string `json:"meta,omitempty"`
}

// DetectionResult 检测结果
type DetectionResult struct {
	URL          string                 `json:"url"`
	StatusCode   int                    `json:"status_code"`
	Technologies []Technology           `json:"technologies"`
	Error        string                 `json:"error,omitempty"`
	ResponseTime int64                  `json:"response_time_ms,omitempty"`
	Headers      map[string]string      `json:"headers,omitempty"`
	Meta         map[string]interface{} `json:"meta,omitempty"`
}

// Detector 指纹识别器
type Detector struct {
	wappalyzer *wappalyzer.Wappalyze
	client     *client.HTTPClient
}

// NewDetector 创建新的检测器
func NewDetector(httpClient *client.HTTPClient) (*Detector, error) {
	wappalyzerClient, err := wappalyzer.New()
	if err != nil {
		return nil, fmt.Errorf("初始化wappalyzer失败: %v", err)
	}

	return &Detector{
		wappalyzer: wappalyzerClient,
		client:     httpClient,
	}, nil
}

// DetectURL 检测单个URL
func (d *Detector) DetectURL(url string) *DetectionResult {
	result := &DetectionResult{
		URL:          url,
		Technologies: []Technology{},
		Headers:      make(map[string]string),
		Meta:         make(map[string]interface{}),
	}

	// 发送HTTP请求
	resp := d.client.Get(url)
	if resp.Error != nil {
		result.Error = resp.Error.Error()
		return result
	}

	result.StatusCode = resp.StatusCode
	
	// 转换Headers为map[string]string格式
	for key, values := range resp.Headers {
		if len(values) > 0 {
			result.Headers[key] = values[0]
		}
	}

	// 使用wappalyzer进行指纹识别
	fingerprints := d.wappalyzer.Fingerprint(resp.Headers, resp.Body)
	
	// 转换结果格式
	for techName := range fingerprints {
		tech := Technology{
			Name: techName,
		}
		
		// 尝试获取更多技术信息（如果wappalyzer提供的话）
		// 这里可以根据需要扩展更多信息
		result.Technologies = append(result.Technologies, tech)
	}

	// 按技术名称排序
	sort.Slice(result.Technologies, func(i, j int) bool {
		return result.Technologies[i].Name < result.Technologies[j].Name
	})

	// 添加一些元信息
	result.Meta["total_technologies"] = len(result.Technologies)
	result.Meta["content_length"] = len(resp.Body)
	result.Meta["server"] = result.Headers["Server"]
	
	return result
}

// DetectURLs 批量检测URL
func (d *Detector) DetectURLs(urls []string) []*DetectionResult {
	results := make([]*DetectionResult, len(urls))
	
	for i, url := range urls {
		results[i] = d.DetectURL(url)
	}
	
	return results
}

// GetTechnologyNames 获取所有检测到的技术名称
func (r *DetectionResult) GetTechnologyNames() []string {
	names := make([]string, len(r.Technologies))
	for i, tech := range r.Technologies {
		names[i] = tech.Name
	}
	return names
}

// GetTechnologyNamesString 获取技术名称的字符串表示
func (r *DetectionResult) GetTechnologyNamesString() string {
	if len(r.Technologies) == 0 {
		return "无检测到技术栈"
	}
	return strings.Join(r.GetTechnologyNames(), ", ")
}

// HasError 检查是否有错误
func (r *DetectionResult) HasError() bool {
	return r.Error != ""
}

// IsSuccessful 检查检测是否成功
func (r *DetectionResult) IsSuccessful() bool {
	return !r.HasError() && r.StatusCode >= 200 && r.StatusCode < 400
}
