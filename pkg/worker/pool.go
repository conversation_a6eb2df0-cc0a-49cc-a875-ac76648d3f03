package worker

import (
	"context"
	"sync"
	"time"

	"wappalyzer-cli/pkg/detector"
)

// Job 工作任务
type Job struct {
	URL   string
	Index int // 用于保持结果顺序
}

// Result 工作结果
type Result struct {
	Index  int
	Result *detector.DetectionResult
}

// Pool worker池
type Pool struct {
	detector    *detector.Detector
	workerCount int
	jobs        chan Job
	results     chan Result
	done        chan bool
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
}

// NewPool 创建新的worker池
func NewPool(detector *detector.Detector, workerCount int) *Pool {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &Pool{
		detector:    detector,
		workerCount: workerCount,
		jobs:        make(chan Job, workerCount*2), // 缓冲区大小为worker数量的2倍
		results:     make(chan Result, workerCount*2),
		done:        make(chan bool),
		ctx:         ctx,
		cancel:      cancel,
	}
}

// Start 启动worker池
func (p *Pool) Start() {
	// 启动workers
	for i := 0; i < p.workerCount; i++ {
		p.wg.Add(1)
		go p.worker(i)
	}
	
	// 启动结果收集器
	go p.resultCollector()
}

// worker 工作协程
func (p *Pool) worker(id int) {
	defer p.wg.Done()
	
	for {
		select {
		case job, ok := <-p.jobs:
			if !ok {
				return // jobs channel已关闭
			}
			
			// 执行检测任务
			result := p.detector.DetectURL(job.URL)
			
			// 发送结果
			select {
			case p.results <- Result{Index: job.Index, Result: result}:
			case <-p.ctx.Done():
				return
			}
			
		case <-p.ctx.Done():
			return
		}
	}
}

// resultCollector 结果收集器（简化版本，实际结果在ProcessURLs中处理）
func (p *Pool) resultCollector() {
	defer close(p.done)
	// 这个函数现在主要用于清理，实际结果处理在ProcessURLs中
}

// ProcessURLs 处理URL列表
func (p *Pool) ProcessURLs(urls []string, progressCallback func(int, int, string)) []*detector.DetectionResult {
	if len(urls) == 0 {
		return nil
	}
	
	// 创建结果切片，保持顺序
	results := make([]*detector.DetectionResult, len(urls))
	resultCount := 0
	
	// 发送所有任务
	go func() {
		defer close(p.jobs)
		for i, url := range urls {
			select {
			case p.jobs <- Job{URL: url, Index: i}:
			case <-p.ctx.Done():
				return
			}
		}
	}()
	
	// 收集结果
	for resultCount < len(urls) {
		select {
		case result := <-p.results:
			results[result.Index] = result.Result
			resultCount++
			
			// 调用进度回调
			if progressCallback != nil {
				progressCallback(resultCount, len(urls), result.Result.URL)
			}
			
		case <-p.ctx.Done():
			// 如果被取消，返回已收集的结果
			return results[:resultCount]
		}
	}
	
	return results
}

// ProcessURLsWithTimeout 带超时的URL处理
func (p *Pool) ProcessURLsWithTimeout(urls []string, timeout time.Duration, progressCallback func(int, int, string)) []*detector.DetectionResult {
	// 创建带超时的context
	ctx, cancel := context.WithTimeout(p.ctx, timeout)
	defer cancel()
	
	// 临时替换context
	oldCtx := p.ctx
	p.ctx = ctx
	defer func() { p.ctx = oldCtx }()
	
	return p.ProcessURLs(urls, progressCallback)
}

// Stop 停止worker池
func (p *Pool) Stop() {
	p.cancel()
	p.wg.Wait()
	close(p.results)
	<-p.done
}

// GetStats 获取统计信息
func (p *Pool) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"worker_count": p.workerCount,
		"jobs_buffer":  cap(p.jobs),
		"results_buffer": cap(p.results),
	}
}

// SetWorkerCount 动态调整worker数量
func (p *Pool) SetWorkerCount(count int) {
	if count <= 0 {
		count = 1
	}
	if count > 100 {
		count = 100
	}
	
	// 注意：这个实现是简化的，实际中需要更复杂的逻辑来动态调整worker数量
	p.workerCount = count
}
