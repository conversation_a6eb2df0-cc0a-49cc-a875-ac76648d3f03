# Wappalyzer CLI

一个高性能的Web服务指纹识别CLI工具，基于[wappalyzergo](https://github.com/projectdiscovery/wappalyzergo)库开发。

## 功能特性

- 🚀 **高性能并发处理** - 支持自定义并发数量，快速批量检测
- 🎯 **多种输入方式** - 支持单个URL、文件输入、stdin管道输入
- 📊 **多种输出格式** - 支持text、json、table、simple四种输出格式
- 🔄 **智能重试机制** - 自动重试失败的请求，提高检测成功率
- 📈 **实时进度显示** - 美观的进度条和实时统计信息
- ⚙️ **灵活配置选项** - 支持自定义超时、重试次数、User-Agent等
- 🛡️ **完善错误处理** - 详细的错误分类和处理机制

## 安装

### 从源码编译

```bash
git clone <repository-url>
cd wappalyzer-cli
go build -o wappalyzer-cli main.go
```

### 使用go install

```bash
go install wappalyzer-cli@latest
```

## 使用方法

### 基本用法

```bash
# 检测单个URL
wappalyzer-cli -u https://example.com

# 从文件读取URL列表
wappalyzer-cli -i urls.txt

# 从stdin读取URL
echo "https://example.com" | wappalyzer-cli

# 使用管道处理
cat urls.txt | wappalyzer-cli
```

### 输出格式

```bash
# 文本格式（默认）
wappalyzer-cli -u https://example.com -f text

# JSON格式
wappalyzer-cli -u https://example.com -f json

# 表格格式
wappalyzer-cli -u https://example.com -f table

# 简洁格式
wappalyzer-cli -u https://example.com -f simple
```

### 性能调优

```bash
# 设置并发数
wappalyzer-cli -i urls.txt -c 20

# 设置超时时间
wappalyzer-cli -i urls.txt -t 15

# 设置重试次数
wappalyzer-cli -i urls.txt -r 5
```

### 高级选项

```bash
# 静默模式
wappalyzer-cli -i urls.txt -s

# 详细输出
wappalyzer-cli -i urls.txt -v

# 自定义User-Agent
wappalyzer-cli -u https://example.com --user-agent "Custom-Agent/1.0"

# 使用代理
wappalyzer-cli -u https://example.com --proxy http://proxy:8080

# 输出到文件
wappalyzer-cli -i urls.txt -o results.json -f json
```

## 命令行选项

| 选项 | 简写 | 默认值 | 描述 |
|------|------|--------|------|
| `--url` | `-u` | - | 要检测的目标URL |
| `--input` | `-i` | - | 包含URL列表的输入文件 |
| `--format` | `-f` | text | 输出格式 (text\|json\|table\|simple) |
| `--output` | `-o` | - | 输出文件路径 |
| `--concurrency` | `-c` | 10 | 并发数量 |
| `--timeout` | `-t` | 10 | HTTP超时时间(秒) |
| `--retries` | `-r` | 3 | 重试次数 |
| `--user-agent` | - | wappalyzer-cli/1.0 | 自定义User-Agent |
| `--proxy` | - | - | 代理服务器 |
| `--silent` | `-s` | false | 静默模式 |
| `--verbose` | `-v` | false | 详细输出模式 |
| `--progress` | `-p` | true | 显示进度条 |

## 输出示例

### 文本格式
```
🌐 https://example.com [200]
   📋 检测到 2 个技术:
      • Python
      • gunicorn:19.9.0
   🖥️  服务器: gunicorn/19.9.0
   📊 响应大小: 405 bytes
```

### JSON格式
```json
{
  "url": "https://example.com",
  "status_code": 200,
  "technologies": [
    {"name": "Python"},
    {"name": "gunicorn:19.9.0"}
  ],
  "headers": {...},
  "meta": {...}
}
```

### 简洁格式
```
https://example.com [200] Python, gunicorn:19.9.0
```

## 性能建议

- 对于大量URL检测，建议设置合适的并发数（10-50）
- 网络较慢时可以增加超时时间
- 使用简洁格式可以减少输出处理时间
- 静默模式可以提高处理速度

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
